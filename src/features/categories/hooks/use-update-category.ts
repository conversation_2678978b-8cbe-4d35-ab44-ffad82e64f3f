import type { CategoryUpdateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  updateCategoryMutation,
  listCategoriesQueryKey,
  getCategoryQueryKey,
} from "~/api/@tanstack/react-query.gen";

import { useCategoryActionsStore } from "../store";

export function useUpdateCategory() {
  const queryClient = useQueryClient();

  const { closeCategoryDialog } = useCategoryActionsStore();

  const mutation = useMutation({
    ...updateCategoryMutation(),
    onSuccess: (data, variables) => {
      // Invalidate categories query to refresh the list
      void queryClient.invalidateQueries({ queryKey: listCategoriesQueryKey() });
      // Invalidate specific category query
      void queryClient.invalidateQueries({ 
        queryKey: getCategoryQueryKey({ path: { id: variables.path.id } }) 
      });

      toast.success("Category updated successfully!", {
        description: `${data.name} has been updated.`,
      });

      closeCategoryDialog();
    },
    onError: (error) => {
      toast.error("Failed to update category", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const updateCategory = (id: string, data: CategoryUpdateRequest) => {
    mutation.mutate({
      path: { id },
      body: {
        name: data.name,
        color: data.color,
        icon: data.icon,
        isExpense: data.isExpense,
      },
    });
  };

  return {
    updateCategory,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
