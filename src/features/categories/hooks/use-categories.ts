import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { listCategoriesOptions } from "~/api/@tanstack/react-query.gen";

import { groupCategoriesByType } from "../utils";

export function useCategories() {
  const {
    data: categoriesData,
    isLoading,
    error,
    refetch,
  } = useQuery(listCategoriesOptions());

  const categories = categoriesData || [];

  const groupedCategories = useMemo(() => {
    return groupCategoriesByType(categories);
  }, [categories]);

  return {
    categories,
    groupedCategories,
    isLoading,
    error,
    refetch,
  };
}
