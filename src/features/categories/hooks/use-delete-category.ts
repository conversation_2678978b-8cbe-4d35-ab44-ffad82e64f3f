import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  deleteCategoryMutation,
  listCategoriesQueryKey,
} from "~/api/@tanstack/react-query.gen";

export function useDeleteCategory() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...deleteCategoryMutation(),
    onSuccess: (_, variables) => {
      // Invalidate categories query to refresh the list
      void queryClient.invalidateQueries({ queryKey: listCategoriesQueryKey() });

      toast.success("Category deleted successfully!");
    },
    onError: (error) => {
      toast.error("Failed to delete category", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const deleteCategory = (id: string) => {
    mutation.mutate({
      path: { id },
    });
  };

  return {
    deleteCategory,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
