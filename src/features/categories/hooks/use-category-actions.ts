import type { Category } from "~/api/types.gen";

import { useConfirm } from "~/features/ui/confirmations/hooks";

import { useCategoryActionsStore } from "../store";
import { useCreateCategory } from "./use-create-category";
import { useUpdateCategory } from "./use-update-category";
import { useDeleteCategory } from "./use-delete-category";

export function useCategoryActions() {
  const ask = useConfirm();

  // Store actions
  const { openCreateCategoryDialog, openEditCategoryDialog, closeCategoryDialog } = useCategoryActionsStore();

  // Mutation hooks
  const { createCategory: createCategoryMutation } = useCreateCategory();
  const { updateCategory: updateCategoryMutation } = useUpdateCategory();
  const { deleteCategory: deleteCategoryMutation } = useDeleteCategory();

  const createCategory = () => {
    openCreateCategoryDialog();
  };

  const editCategory = (category: Category) => {
    openEditCategoryDialog(category);
  };

  const deleteCategory = async (category: Category) => {
    const confirmed = await ask({
      title: "Delete category",
      description: `Are you sure you want to delete "${category.name}"? This action cannot be undone.`,
      confirmText: "Yes, delete",
      cancelText: "Cancel",
      variant: "destructive",
    });

    if (confirmed) {
      deleteCategoryMutation(category.id);
    }
  };

  return {
    // Dialog actions
    createCategory,
    editCategory,
    deleteCategory,
    closeCategoryDialog,

    // Direct mutation functions (for form submissions)
    createCategoryMutation,
    updateCategoryMutation,
    deleteCategoryMutation,
  };
}
